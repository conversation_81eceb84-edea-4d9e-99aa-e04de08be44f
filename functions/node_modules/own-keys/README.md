# own-keys <sup>[![Version Badge][npm-version-svg]][package-url]</sup>

[![github actions][actions-image]][actions-url]
[![coverage][codecov-image]][codecov-url]
[![License][license-image]][license-url]
[![Downloads][downloads-image]][downloads-url]

[![npm badge][npm-badge-png]][package-url]

Robustly get an object's own property keys (strings and symbols), including non-enumerables when possible.

## Getting started

```sh
npm install --save own-keys
```

## Usage/Examples

```js
var ownKeys = require('own-keys');
var assert = require('assert');

assert.deepEqual(ownKeys({ a: 1, b: 2 }), ['a', 'b']);
assert.deepEqual(ownKeys([1, 2, 3]), [0, 1, 2, 'length']);
```

## Tests
Simply clone the repo, `npm install`, and run `npm test`

[package-url]: https://npmjs.org/package/own-keys
[npm-version-svg]: https://versionbadg.es/ljharb/own-keys.svg
[deps-svg]: https://david-dm.org/ljharb/own-keys.svg
[deps-url]: https://david-dm.org/ljharb/own-keys
[dev-deps-svg]: https://david-dm.org/ljharb/own-keys/dev-status.svg
[dev-deps-url]: https://david-dm.org/ljharb/own-keys#info=devDependencies
[npm-badge-png]: https://nodei.co/npm/own-keys.png?downloads=true&stars=true
[license-image]: https://img.shields.io/npm/l/own-keys.svg
[license-url]: LICENSE
[downloads-image]: https://img.shields.io/npm/dm/own-keys.svg
[downloads-url]: https://npm-stat.com/charts.html?package=own-keys
[codecov-image]: https://codecov.io/gh/ljharb/own-keys/branch/main/graphs/badge.svg
[codecov-url]: https://app.codecov.io/gh/ljharb/own-keys/
[actions-image]: https://img.shields.io/endpoint?url=https://github-actions-badge-u3jn4tfpocch.runkit.sh/ljharb/own-keys
[actions-url]: https://github.com/ljharb/own-keys/actions
