import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../core/services/firebase_functions_service.dart';
import '../../../../core/utils/logger.dart';
import '../../providers/onboarding_provider.dart';

class CompleteStep extends ConsumerStatefulWidget {
  const CompleteStep({super.key});

  @override
  ConsumerState<CompleteStep> createState() => _CompleteStepState();
}

class _CompleteStepState extends ConsumerState<CompleteStep> {
  bool _isGeneratingMealPlan = false;
  String? _generationError;

  @override
  void initState() {
    super.initState();
    // Start completion process when this step is reached
    WidgetsBinding.instance.addPostFrameCallback((_) {
      _completeOnboarding();
    });
  }

  Future<void> _completeOnboarding() async {
    setState(() {
      _isGeneratingMealPlan = true;
      _generationError = null;
    });

    try {
      final onboardingData = ref.read(onboardingNotifierProvider).data;
      final firebaseFunctionsService = ref.read(firebaseFunctionsServiceProvider);

      AppLogger.info('Starting meal plan generation for onboarding completion');

      // Prepare preferences for the API call
      final preferences = {
        'dietaryRestrictions': onboardingData.dietaryRestrictions,
        'allergies': onboardingData.allergies,
        'cuisinePreferences': onboardingData.favoriteCuisines,
        'mealsPerDay': onboardingData.mealsPerDay,
        'calorieGoal': onboardingData.dailyCalorieGoal ?? 2000,
        'proteinGoal': 0, // Can be calculated based on goals
        'carbsGoal': 0,   // Can be calculated based on goals
        'fatGoal': 0,     // Can be calculated based on goals
      };

      // Generate initial meal plan
      final mealPlanResponse = await firebaseFunctionsService.generateMealPlan(
        preferences: preferences,
        duration: 3, // Generate a 3-day meal plan
      );

      AppLogger.info('Meal plan generated successfully: ${mealPlanResponse['success']}');

      setState(() {
        _isGeneratingMealPlan = false;
      });

      // Wait a moment to show success, then complete onboarding
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        // Complete onboarding process
        await ref.read(onboardingNotifierProvider.notifier).completeOnboarding();
      }
    } catch (e) {
      AppLogger.warning('Error during onboarding completion: $e');

      setState(() {
        _isGeneratingMealPlan = false;
        _generationError = e.toString();
      });

      // Even if meal plan generation fails, complete onboarding after a delay
      await Future.delayed(const Duration(seconds: 2));

      if (mounted) {
        await ref.read(onboardingNotifierProvider.notifier).completeOnboarding();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.all(24.0),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          // Success animation or icon
          Container(
            width: 120,
            height: 120,
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primary.withOpacity(0.1),
              shape: BoxShape.circle,
            ),
            child: Icon(
              Icons.check_circle,
              size: 60,
              color: Theme.of(context).colorScheme.primary,
            ),
          ),

          const SizedBox(height: 32),

          // Success title
          Text(
            'تم الإعداد بنجاح!',
            style: Theme.of(context).textTheme.headlineLarge?.copyWith(
              fontWeight: FontWeight.bold,
              color: Theme.of(context).colorScheme.onBackground,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          // Success message
          Text(
            'أصبح كل شيء جاهزاً! يمكنك الآن البدء في استخدام التطبيق',
            style: Theme.of(context).textTheme.bodyLarge?.copyWith(
              color: Theme.of(context).colorScheme.onBackground.withOpacity(0.7),
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 24),

          // Settings info message
          Container(
            padding: const EdgeInsets.all(16),
            margin: const EdgeInsets.symmetric(horizontal: 16),
            decoration: BoxDecoration(
              color: Theme.of(context).colorScheme.primaryContainer.withOpacity(0.3),
              borderRadius: BorderRadius.circular(12),
              border: Border.all(
                color: Theme.of(context).colorScheme.primary.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.settings,
                  color: Theme.of(context).colorScheme.primary,
                  size: 24,
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Text(
                    'يمكنك تعديل جميع هذه الإعدادات لاحقاً من صفحة الملف الشخصي في الإعدادات',
                    style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                      color: Theme.of(context).colorScheme.primary,
                      height: 1.4,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 32),

          // Loading indicator and status
          if (_isGeneratingMealPlan) ...[
            const CircularProgressIndicator(),
            const SizedBox(height: 16),
            Text(
              'جاري إنشاء خطة الوجبات الخاصة بك...',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.onBackground.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'قد يستغرق هذا بضع ثوانٍ',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onBackground.withOpacity(0.5),
              ),
              textAlign: TextAlign.center,
            ),
          ] else if (_generationError != null) ...[
            Icon(
              Icons.warning_amber_rounded,
              size: 48,
              color: Theme.of(context).colorScheme.error,
            ),
            const SizedBox(height: 16),
            Text(
              'حدث خطأ في إنشاء خطة الوجبات',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.error,
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'يمكنك إنشاء خطة جديدة لاحقاً من الصفحة الرئيسية',
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Theme.of(context).colorScheme.onBackground.withOpacity(0.6),
              ),
              textAlign: TextAlign.center,
            ),
          ] else ...[
            Icon(
              Icons.restaurant_menu,
              size: 48,
              color: Theme.of(context).colorScheme.primary,
            ),
            const SizedBox(height: 16),
            Text(
              'تم إنشاء خطة الوجبات بنجاح!',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).colorScheme.primary,
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ],
      ),
    );
  }
}
